<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Eventify - Discover and host amazing events. Join thousands of events or create your own. From concerts to conferences, we've got you covered.">
    <meta name="keywords" content="events, tickets, hosting, concerts, conferences, meetups, eventify">
    <meta name="author" content="Iribama Worlu">
    <meta property="og:title" content="Eventify | Discover & Host Amazing Events">
    <meta property="og:description" content="Join thousands of events or create your own. From concerts to conferences, we've got you covered.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://eventify.com">
    <meta property="og:image" content="https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&w=1200&h=630&fit=crop">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Eventify | Discover & Host Amazing Events">
    <meta name="twitter:description" content="Join thousands of events or create your own. From concerts to conferences, we've got you covered.">
    <meta name="twitter:image" content="https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&w=1200&h=630&fit=crop">
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🎉%3C/text%3E%3C/svg%3E">
    <title>Eventify | Discover & Host Amazing Events</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                        dark: '#0f172a',
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                    }
                }
            }
        }
    </script>
    <script src="script.js" defer></script>
</head>
<body class="bg-gray-50 dark:bg-dark text-gray-800 dark:text-gray-200 transition-colors duration-300">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 bg-white/80 dark:bg-dark/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="#" class="flex items-center">
                        <span class="text-primary text-2xl font-bold">Eventify</span>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#discover" class="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary transition">Discover</a>
                    <a href="#host" class="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary transition">Host</a>
                    <a href="#features" class="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary transition">Features</a>
                    <a href="#testimonials" class="text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary transition">Testimonials</a>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:block"></i>
                    </button>
                    <button class="md:hidden p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition" id="mobile-menu-button">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="hidden md:flex space-x-2">
                        <a href="login.html" class="px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition">Log In</a>
                        <a href="signup.html" class="px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg transition">Sign Up</a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="#discover" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary hover:bg-gray-100 dark:hover:bg-gray-700 transition">Discover</a>
                <a href="#host" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary hover:bg-gray-100 dark:hover:bg-gray-700 transition">Host</a>
                <a href="#features" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary hover:bg-gray-100 dark:hover:bg-gray-700 transition">Features</a>
                <a href="#testimonials" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary hover:bg-gray-100 dark:hover:bg-gray-700 transition">Testimonials</a>
                <div class="pt-4 border-t border-gray-200 dark:border-gray-700">
                    <a href="login.html" class="block px-3 py-2 rounded-md text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-secondary hover:bg-gray-100 dark:hover:bg-gray-700 transition">Log In</a>
                    <a href="signup.html" class="block px-3 py-2 mt-1 bg-primary hover:bg-indigo-500 text-white rounded-md text-base font-medium transition">Sign Up</a>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Hero Section -->
    <section class="relative pt-24 pb-32 md:pt-32 md:pb-40 overflow-hidden">
        <div class="absolute inset-0 hero-gradient opacity-95"></div>
        <div class="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')] bg-cover bg-center opacity-20"></div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-12 md:mb-0 animate-fade-in">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">Discover & Host <span class="text-secondary">Amazing</span> Events</h1>
                    <p class="text-xl text-white/90 mb-8">Join thousands of events or create your own. From concerts to conferences, we've got you covered.</p>
                    <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                        <a href="events.html" class="px-8 py-3 bg-white text-primary rounded-lg font-medium hover:bg-gray-100 transition duration-300 text-center">Explore Events</a>
                        <a href="create-event.html" class="px-8 py-3 bg-transparent border-2 border-white text-white rounded-lg font-medium hover:bg-white/10 transition duration-300 text-center">Host an Event</a>
                    </div>
                </div>
                <div class="md:w-1/2 animate-slide-up delay-100">
                    <div class="glass-card rounded-2xl p-6 shadow-xl">
                        <div class="relative overflow-hidden rounded-xl h-80">
                            <div class="absolute inset-0 bg-gradient-to-br from-primary to-secondary opacity-20"></div>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center p-6">
                                    <div class="text-4xl font-bold text-white mb-4">Upcoming Event</div>
                                    <div class="text-xl text-white mb-6">Summer Music Festival 2023</div>
                                    <div class="flex justify-center space-x-2 mb-6" id="countdown">
                                        <div class="countdown-number">
                                            <div class="countdown-flip bg-white text-primary rounded-lg w-16 h-16 flex items-center justify-center text-2xl font-bold" id="days">00</div>
                                            <div class="text-white text-xs mt-1">Days</div>
                                        </div>
                                        <div class="countdown-number">
                                            <div class="countdown-flip bg-white text-primary rounded-lg w-16 h-16 flex items-center justify-center text-2xl font-bold" id="hours">00</div>
                                            <div class="text-white text-xs mt-1">Hours</div>
                                        </div>
                                        <div class="countdown-number">
                                            <div class="countdown-flip bg-white text-primary rounded-lg w-16 h-16 flex items-center justify-center text-2xl font-bold" id="minutes">00</div>
                                            <div class="text-white text-xs mt-1">Mins</div>
                                        </div>
                                        <div class="countdown-number">
                                            <div class="countdown-flip bg-white text-primary rounded-lg w-16 h-16 flex items-center justify-center text-2xl font-bold" id="seconds">00</div>
                                            <div class="text-white text-xs mt-1">Secs</div>
                                        </div>
                                    </div>
                                    <a href="#" class="inline-block px-6 py-2 bg-white text-primary rounded-lg font-medium hover:bg-gray-100 transition">Get Tickets</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white dark:from-dark to-transparent"></div>
    </section>
    
    <!-- Discover Events Section -->
    <section id="discover" class="py-16 bg-white dark:bg-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-fade-in">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Discover <span class="text-primary">Events</span> Near You</h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Find events that match your interests. Music, sports, business, and more.</p>
            </div>
            
            <!-- Event Categories -->
            <div class="mb-12 animate-slide-up delay-100">
                <div class="flex flex-wrap justify-center gap-4">
                    <button class="px-6 py-2 bg-primary text-white rounded-full hover:bg-indigo-500 transition" data-filter="all">All Events</button>
                    <button class="px-6 py-2 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition" data-filter="music">Music</button>
                    <button class="px-6 py-2 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition" data-filter="sports">Sports</button>
                    <button class="px-6 py-2 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition" data-filter="business">Business</button>
                    <button class="px-6 py-2 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition" data-filter="food">Food & Drink</button>
                    <button class="px-6 py-2 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition" data-filter="arts">Arts</button>
                    <button class="px-6 py-2 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition" data-filter="technology">Technology</button>
                </div>
            </div>
            
            <!-- Event Filters -->
            <div class="mb-8 bg-gray-50 dark:bg-gray-800 rounded-xl p-4 shadow-sm animate-slide-up delay-200">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div class="flex-1">
                        <input type="text" placeholder="Search events..." class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    <div class="flex flex-col sm:flex-row gap-4">
                        <select class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option>Any Date</option>
                            <option>Today</option>
                            <option>Tomorrow</option>
                            <option>This Week</option>
                            <option>This Weekend</option>
                            <option>Next Week</option>
                        </select>
                        <select class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option>Any Location</option>
                            <option>New York</option>
                            <option>Los Angeles</option>
                            <option>Chicago</option>
                            <option>Houston</option>
                            <option>Miami</option>
                        </select>
                        <select class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option>Any Price</option>
                            <option>Free</option>
                            <option>Under $25</option>
                            <option>$25 - $50</option>
                            <option>$50 - $100</option>
                            <option>Over $100</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Event Cards -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 animate-slide-up delay-300">
                <!-- Event Card 1 -->
                <div class="event-card bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition duration-300">
                    <div class="relative overflow-hidden h-48">
                        <div class="event-image absolute inset-0 bg-[url('https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')] bg-cover bg-center transition duration-500"></div>
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-primary text-white text-xs font-semibold rounded-full">Trending</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold">Summer Music Festival</h3>
                            <span class="text-lg font-bold text-primary">$49</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            <span>Sat, Jun 10 • 4:00 PM</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>Central Park, New York</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="flex -space-x-2">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/women/44.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/men/32.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/women/68.jpg" alt="Attendee">
                                <div class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-xs">+12</div>
                            </div>
                            <a href="#" class="px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg text-sm font-medium transition">Details</a>
                        </div>
                    </div>
                </div>
                
                <!-- Event Card 2 -->
                <div class="event-card bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition duration-300">
                    <div class="relative overflow-hidden h-48">
                        <div class="event-image absolute inset-0 bg-[url('https://images.unsplash.com/photo-1492684223066-81342ee5ff30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')] bg-cover bg-center transition duration-500"></div>
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-green-500 text-white text-xs font-semibold rounded-full">Free</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold">Community Yoga in the Park</h3>
                            <span class="text-lg font-bold text-primary">Free</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            <span>Sun, Jun 11 • 9:00 AM</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>Riverside Park, New York</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="flex -space-x-2">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/women/33.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/men/75.jpg" alt="Attendee">
                                <div class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-xs">+8</div>
                            </div>
                            <a href="#" class="px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg text-sm font-medium transition">Details</a>
                        </div>
                    </div>
                </div>
                
                <!-- Event Card 3 -->
                <div class="event-card bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition duration-300">
                    <div class="relative overflow-hidden h-48">
                        <div class="event-image absolute inset-0 bg-[url('https://images.unsplash.com/photo-1531058020387-3be344556be6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')] bg-cover bg-center transition duration-500"></div>
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-yellow-500 text-white text-xs font-semibold rounded-full">Last Tickets</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold">Tech Conference 2023</h3>
                            <span class="text-lg font-bold text-primary">$199</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            <span>Fri-Sun, Jun 16-18 • 9:00 AM</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>Javits Center, New York</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="flex -space-x-2">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/men/22.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/women/41.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/men/64.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/women/63.jpg" alt="Attendee">
                                <div class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-xs">+24</div>
                            </div>
                            <a href="#" class="px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg text-sm font-medium transition">Details</a>
                        </div>
                    </div>
                </div>
                
                <!-- Event Card 4 -->
                <div class="event-card bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition duration-300">
                    <div class="relative overflow-hidden h-48">
                        <div class="event-image absolute inset-0 bg-[url('https://images.unsplash.com/photo-1540039155733-5bb30b53aa14?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1974&q=80')] bg-cover bg-center transition duration-500"></div>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold">Art Gallery Opening</h3>
                            <span class="text-lg font-bold text-primary">$25</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            <span>Thu, Jun 15 • 6:00 PM</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>Modern Art Museum, New York</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="flex -space-x-2">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/women/28.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/men/36.jpg" alt="Attendee">
                                <div class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-xs">+5</div>
                            </div>
                            <a href="#" class="px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg text-sm font-medium transition">Details</a>
                        </div>
                    </div>
                </div>
                
                <!-- Event Card 5 -->
                <div class="event-card bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition duration-300">
                    <div class="relative overflow-hidden h-48">
                        <div class="event-image absolute inset-0 bg-[url('https://images.unsplash.com/photo-1519671482749-fd09be7ccebf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')] bg-cover bg-center transition duration-500"></div>
                        <div class="absolute top-4 right-4">
                            <span class="px-3 py-1 bg-red-500 text-white text-xs font-semibold rounded-full">Sold Out</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold">Food & Wine Festival</h3>
                            <span class="text-lg font-bold text-primary">$75</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            <span>Sat, Jun 17 • 12:00 PM</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>Pier 76, New York</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="flex -space-x-2">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/men/43.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/women/67.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/men/55.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/women/72.jpg" alt="Attendee">
                                <div class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-xs">+32</div>
                            </div>
                            <a href="#" class="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-300 rounded-lg text-sm font-medium cursor-not-allowed">Sold Out</a>
                        </div>
                    </div>
                </div>
                
                <!-- Event Card 6 -->
                <div class="event-card bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition duration-300">
                    <div class="relative overflow-hidden h-48">
                        <div class="event-image absolute inset-0 bg-[url('https://images.unsplash.com/photo-1541178735493-479c1a27ed24?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80')] bg-cover bg-center transition duration-500"></div>
                    </div>
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-2">
                            <h3 class="text-xl font-bold">Startup Networking Mixer</h3>
                            <span class="text-lg font-bold text-primary">$35</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-calendar-alt mr-2"></i>
                            <span>Tue, Jun 20 • 7:00 PM</span>
                        </div>
                        <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>WeWork, Soho</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <div class="flex -space-x-2">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/men/29.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/women/31.jpg" alt="Attendee">
                                <img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/men/47.jpg" alt="Attendee">
                                <div class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-xs">+9</div>
                            </div>
                            <a href="#" class="px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg text-sm font-medium transition">Details</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-12 animate-fade-in delay-400">
                <a href="events.html" class="inline-block px-8 py-3 bg-primary hover:bg-indigo-500 text-white rounded-lg font-medium transition">View All Events</a>
            </div>
        </div>
    </section>
    
    <!-- Host Events Section -->
    <section id="host" class="py-16 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-fade-in">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Host Your <span class="text-secondary">Event</span> With Us</h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Create, manage, and promote your event with our powerful tools.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center animate-slide-up delay-100">
                <div>
                    <div class="glass-card rounded-2xl p-8 shadow-lg mb-8">
                        <h3 class="text-2xl font-bold mb-4">Why Host With Eventify?</h3>
                        <ul class="space-y-4">
                            <li class="flex items-start">
                                <div class="flex-shrink-0 h-6 w-6 text-primary">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <p class="ml-3 text-gray-700 dark:text-gray-300">Reach thousands of potential attendees</p>
                            </li>
                            <li class="flex items-start">
                                <div class="flex-shrink-0 h-6 w-6 text-primary">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <p class="ml-3 text-gray-700 dark:text-gray-300">Easy ticket sales and registration</p>
                            </li>
                            <li class="flex items-start">
                                <div class="flex-shrink-0 h-6 w-6 text-primary">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <p class="ml-3 text-gray-700 dark:text-gray-300">Real-time analytics and reporting</p>
                            </li>
                            <li class="flex items-start">
                                <div class="flex-shrink-0 h-6 w-6 text-primary">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <p class="ml-3 text-gray-700 dark:text-gray-300">Marketing tools to promote your event</p>
                            </li>
                            <li class="flex items-start">
                                <div class="flex-shrink-0 h-6 w-6 text-primary">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <p class="ml-3 text-gray-700 dark:text-gray-300">Dedicated support for event organizers</p>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="create-event.html" class="px-6 py-3 bg-primary hover:bg-indigo-500 text-white rounded-lg font-medium transition text-center">Start Creating</a>
                        <a href="#" class="px-6 py-3 bg-transparent border-2 border-primary text-primary hover:bg-primary/10 dark:hover:bg-primary/20 rounded-lg font-medium transition text-center">Learn More</a>
                    </div>
                </div>
                
                <div class="relative">
                    <div class="glass-card rounded-2xl p-6 shadow-xl overflow-hidden">
                        <div class="bg-white dark:bg-gray-800 rounded-lg p-6">
                            <h3 class="text-xl font-bold mb-4">Create New Event</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Event Name</label>
                                    <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Enter event name">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Event Type</label>
                                    <select class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                        <option>Select event type</option>
                                        <option>Music</option>
                                        <option>Sports</option>
                                        <option>Business</option>
                                        <option>Food & Drink</option>
                                        <option>Arts</option>
                                        <option>Technology</option>
                                    </select>
                                </div>
                                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Start Date</label>
                                        <input type="date" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">End Date</label>
                                        <input type="date" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Location</label>
                                    <input type="text" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Enter location or venue">
                                </div>
                                <div class="pt-2">
                                    <button class="w-full px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg font-medium transition">Continue Setup</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Features Section -->
    <section id="features" class="py-16 bg-white dark:bg-dark">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-fade-in">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Powerful <span class="text-primary">Features</span></h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Everything you need to discover and host amazing events.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 animate-slide-up delay-100">
                <!-- Feature 1 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition duration-300">
                    <div class="w-14 h-14 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-ticket-alt text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Easy Ticketing</h3>
                    <p class="text-gray-600 dark:text-gray-400">Sell tickets online with our simple and secure platform. Multiple ticket types, discounts, and more.</p>
                </div>
                
                <!-- Feature 2 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition duration-300">
                    <div class="w-14 h-14 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-chart-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Real-time Analytics</h3>
                    <p class="text-gray-600 dark:text-gray-400">Track ticket sales, attendance, and revenue with our comprehensive analytics dashboard.</p>
                </div>
                
                <!-- Feature 3 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition duration-300">
                    <div class="w-14 h-14 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-bullhorn text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Marketing Tools</h3>
                    <p class="text-gray-600 dark:text-gray-400">Promote your event with email campaigns, social media integration, and discount codes.</p>
                </div>
                
                <!-- Feature 4 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition duration-300">
                    <div class="w-14 h-14 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-qrcode text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Check-in System</h3>
                    <p class="text-gray-600 dark:text-gray-400">Fast and reliable check-in with QR code scanning and mobile ticket validation.</p>
                </div>
                
                <!-- Feature 5 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition duration-300">
                    <div class="w-14 h-14 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-users text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Attendee Management</h3>
                    <p class="text-gray-600 dark:text-gray-400">Manage your attendees, send updates, and collect feedback all in one place.</p>
                </div>
                
                <!-- Feature 6 -->
                <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition duration-300">
                    <div class="w-14 h-14 bg-primary/10 dark:bg-primary/20 rounded-lg flex items-center justify-center mb-6">
                        <i class="fas fa-mobile-alt text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Mobile Friendly</h3>
                    <p class="text-gray-600 dark:text-gray-400">Fully responsive design works perfectly on any device, from desktop to smartphone.</p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Testimonials Section -->
    <section id="testimonials" class="py-16 bg-gray-50 dark:bg-gray-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-fade-in">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">What Our <span class="text-secondary">Users</span> Say</h2>
                <p class="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">Don't just take our word for it. Here's what our community has to say.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 animate-slide-up delay-100">
                <!-- Testimonial 1 -->
                <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition duration-300">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0">
                            <img class="w-12 h-12 rounded-full" src="https://randomuser.me/api/portraits/women/32.jpg" alt="User">
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-bold">Sarah Johnson</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">Event Organizer</p>
                        </div>
                    </div>
                    <div class="text-gray-700 dark:text-gray-300">
                        <p>"Eventify has completely transformed how we organize our annual conference. The ticketing system is seamless, and the analytics help us understand our audience better."</p>
                    </div>
                    <div class="mt-4 flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
                
                <!-- Testimonial 2 -->
                <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition duration-300">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0">
                            <img class="w-12 h-12 rounded-full" src="https://randomuser.me/api/portraits/men/54.jpg" alt="User">
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-bold">Michael Chen</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">Music Festival Director</p>
                        </div>
                    </div>
                    <div class="text-gray-700 dark:text-gray-300">
                        <p>"The platform's ease of use and powerful features allowed us to sell out our festival tickets in record time. The mobile check-in system saved us hours of work."</p>
                    </div>
                    <div class="mt-4 flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star-half-alt"></i>
                    </div>
                </div>
                
                <!-- Testimonial 3 -->
                <div class="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-lg hover:shadow-xl transition duration-300">
                    <div class="flex items-center mb-4">
                        <div class="flex-shrink-0">
                            <img class="w-12 h-12 rounded-full" src="https://randomuser.me/api/portraits/women/68.jpg" alt="User">
                        </div>
                        <div class="ml-4">
                            <h4 class="text-lg font-bold">Jessica Williams</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">Community Manager</p>
                        </div>
                    </div>
                    <div class="text-gray-700 dark:text-gray-300">
                        <p>"As someone who attends many events, I love how easy Eventify makes it to discover new experiences. The recommendations are always spot on!"</p>
                    </div>
                    <div class="mt-4 flex text-yellow-400">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- CTA Section -->
    <section class="py-16 bg-gradient-to-r from-primary to-secondary">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6 animate-fade-in">Ready to Join the Event Revolution?</h2>
            <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto animate-fade-in delay-100">Sign up now and start discovering or hosting amazing events today.</p>
            <div class="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4 animate-slide-up delay-200">
                <a href="signup.html" class="px-8 py-3 bg-white text-primary rounded-lg font-medium hover:bg-gray-100 transition duration-300 text-center">Sign Up Free</a>
                <a href="#" class="px-8 py-3 bg-transparent border-2 border-white text-white rounded-lg font-medium hover:bg-white/10 transition duration-300 text-center">Learn More</a>
            </div>
        </div>
    </section>
    
    <!-- Footer -->
    <footer class="bg-white dark:bg-dark border-t border-gray-200 dark:border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold text-primary mb-4">Eventify</h3>
                    <p class="text-gray-600 dark:text-gray-400">The premier platform for discovering and hosting amazing events.</p>
                    <div class="flex space-x-4 mt-6">
                        <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Discover</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Popular Events</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Upcoming Events</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Nearby Events</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Online Events</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Host</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Create Event</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Pricing</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Resources</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Community</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-4">Company</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">About Us</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Careers</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Blog</a></li>
                        <li><a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Contact</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-200 dark:border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-600 dark:text-gray-400">© 2023 Eventify. All rights reserved.</p>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Privacy Policy</a>
                    <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Terms of Service</a>
                    <a href="#" class="text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-secondary transition">Cookies</a>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Event Modal (hidden by default) -->
    <div id="event-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
            </div>
            
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            
            <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <div class="flex justify-between items-start">
                                <h3 class="text-2xl leading-6 font-bold text-gray-900 dark:text-white" id="modal-title">Summer Music Festival</h3>
                                <button type="button" class="modal-close text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            
                            <div class="mt-6">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div class="md:col-span-2">
                                        <div class="relative h-64 md:h-80 rounded-xl overflow-hidden mb-6">
                                            <img src="https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" alt="Event" class="w-full h-full object-cover">
                                        </div>
                                        
                                        <div class="mb-6">
                                            <h4 class="text-lg font-bold mb-2">About This Event</h4>
                                            <p class="text-gray-600 dark:text-gray-400">Join us for the biggest music festival of the summer! Featuring top artists across multiple genres, food trucks, art installations, and more. This three-day event promises unforgettable experiences and memories that will last a lifetime.</p>
                                        </div>
                                        
                                        <div class="mb-6">
                                            <h4 class="text-lg font-bold mb-2">Event Schedule</h4>
                                            <div class="space-y-4">
                                                <div class="flex items-start">
                                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center text-primary mr-4">
                                                        <i class="fas fa-calendar-day"></i>
                                                    </div>
                                                    <div>
                                                        <h5 class="font-medium">Day 1 - Friday</h5>
                                                        <p class="text-sm text-gray-600 dark:text-gray-400">4:00 PM - Gates Open<br>5:30 PM - Opening Act<br>7:00 PM - Headliner Performance</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-start">
                                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center text-primary mr-4">
                                                        <i class="fas fa-calendar-day"></i>
                                                    </div>
                                                    <div>
                                                        <h5 class="font-medium">Day 2 - Saturday</h5>
                                                        <p class="text-sm text-gray-600 dark:text-gray-400">12:00 PM - Gates Open<br>1:30 PM - Local Bands Showcase<br>4:00 PM - Main Stage Performances</p>
                                                    </div>
                                                </div>
                                                <div class="flex items-start">
                                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center text-primary mr-4">
                                                        <i class="fas fa-calendar-day"></i>
                                                    </div>
                                                    <div>
                                                        <h5 class="font-medium">Day 3 - Sunday</h5>
                                                        <p class="text-sm text-gray-600 dark:text-gray-400">2:00 PM - Gates Open<br>3:30 PM - Acoustic Sessions<br>6:00 PM - Closing Headliner</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-6">
                                            <h4 class="text-lg font-bold mb-2">Location</h4>
                                            <div class="rounded-xl overflow-hidden h-48 bg-gray-200 dark:bg-gray-700">
                                                <!-- Map placeholder -->
                                                <div class="w-full h-full flex items-center justify-center">
                                                    <i class="fas fa-map-marked-alt text-3xl text-gray-400"></i>
                                                </div>
                                            </div>
                                            <p class="mt-2 text-gray-600 dark:text-gray-400">Central Park, New York, NY 10024</p>
                                        </div>
                                    </div>
                                    
                                    <div>
                                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 sticky top-4">
                                            <div class="flex justify-between items-center mb-4">
                                                <h4 class="font-bold">Ticket Options</h4>
                                                <div class="text-sm text-gray-600 dark:text-gray-400">From $49</div>
                                            </div>
                                            
                                            <div class="space-y-4 mb-6">
                                                <div class="ticket-option border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:border-primary dark:hover:border-secondary transition duration-300 cursor-pointer">
                                                    <div class="flex justify-between items-start mb-2">
                                                        <h5 class="font-medium">General Admission</h5>
                                                        <span class="font-bold text-primary">$49</span>
                                                    </div>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Access to all general areas of the festival</p>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">300+ available</div>
                                                </div>
                                                
                                                <div class="ticket-option border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:border-primary dark:hover:border-secondary transition duration-300 cursor-pointer">
                                                    <div class="flex justify-between items-start mb-2">
                                                        <h5 class="font-medium">VIP Experience</h5>
                                                        <span class="font-bold text-primary">$149</span>
                                                    </div>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">VIP viewing areas, premium restrooms, and exclusive bar access</p>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">50 available</div>
                                                </div>
                                                
                                                <div class="ticket-option border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:border-primary dark:hover:border-secondary transition duration-300 cursor-pointer">
                                                    <div class="flex justify-between items-start mb-2">
                                                        <h5 class="font-medium">Platinum Package</h5>
                                                        <span class="font-bold text-primary">$299</span>
                                                    </div>
                                                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Front row access, meet & greet opportunities, and premium parking</p>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400">10 available</div>
                                                </div>
                                            </div>
                                            
                                            <button class="w-full py-3 bg-primary hover:bg-indigo-500 text-white rounded-lg font-medium transition">Get Tickets</button>
                                            
                                            <div class="mt-4 text-center text-sm text-gray-600 dark:text-gray-400">
                                                <p>Have a promo code? <a href="#" class="text-primary hover:underline">Apply here</a></p>
                                            </div>
                                            
                                            <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
                                                <h5 class="font-medium mb-2">Share this event</h5>
                                                <div class="flex justify-center space-x-4">
                                                    <a href="#" class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-primary hover:text-white transition">
                                                        <i class="fab fa-facebook-f"></i>
                                                    </a>
                                                    <a href="#" class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-primary hover:text-white transition">
                                                        <i class="fab fa-twitter"></i>
                                                    </a>
                                                    <a href="#" class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-primary hover:text-white transition">
                                                        <i class="fab fa-instagram"></i>
                                                    </a>
                                                    <a href="#" class="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:bg-primary hover:text-white transition">
                                                        <i class="fas fa-envelope"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Toast Notification (hidden by default) -->
    <div id="toast" class="fixed bottom-4 right-4 hidden">
        <div class="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center">
            <i class="fas fa-check-circle mr-2"></i>
            <span>Event added to your favorites!</span>
        </div>
    </div>
    
    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });
        
        // Note: Theme toggle and countdown timer are handled by script.js
        
        // Event modal
        const modal = document.getElementById('event-modal');
        const modalClose = document.querySelector('.modal-close');
        
        document.querySelectorAll('a[href="#"]').forEach(link => {
            link.addEventListener('click', function(e) {
                if (this.textContent.includes('Details')) {
                    e.preventDefault();
                    modal.classList.remove('hidden');
                    document.body.classList.add('overflow-hidden');
                }
            });
        });
        
        modalClose.addEventListener('click', function() {
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        });
    </script>
</body>
</html>