<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Event | Eventify</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                        dark: '#0f172a',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-dark text-gray-800 dark:text-gray-200 relative">
    <!-- Elegant Booking Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 dark:from-gray-900 dark:via-teal-900/20 dark:to-emerald-900/20"></div>
    <div class="fixed inset-0 bg-[url('data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%2310b981" fill-opacity="0.05" fill-rule="evenodd"%3E%3Cpath d="M20 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0-20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm20 0c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8zm0 20c0 4.4-3.6 8-8 8s-8-3.6-8-8 3.6-8 8-8 8 3.6 8 8z"/%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

    <!-- Ticket-themed elements -->
    <div class="fixed top-20 left-20 w-32 h-32 bg-gradient-to-br from-emerald-400/20 to-teal-500/20 rounded-lg rotate-12 blur-xl animate-pulse"></div>
    <div class="fixed bottom-20 right-20 w-40 h-40 bg-gradient-to-br from-cyan-400/20 to-blue-500/20 rounded-full blur-xl animate-float"></div>
    <div class="fixed top-1/2 right-10 w-24 h-24 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-lg rotate-45 blur-lg animate-bounce-slow"></div>
    <!-- Navigation -->
    <nav class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-lg shadow-sm border-b border-gray-200 dark:border-gray-700 relative z-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="Evnts.html" class="flex items-center">
                        <span class="text-primary text-2xl font-bold">Eventify</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="Evnts.html" class="text-gray-600 dark:text-gray-400 hover:text-primary transition">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Event Details -->
            <div>
                <div class="bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-2xl shadow-2xl overflow-hidden border border-white/20 dark:border-gray-700/50">
                    <div class="h-64 bg-gradient-to-br from-primary to-secondary"></div>
                    <div class="p-8">
                        <h1 class="text-3xl font-bold mb-4">Summer Music Festival</h1>
                        <div class="space-y-3 mb-6">
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-calendar-alt mr-3 text-primary"></i>
                                <span>Saturday, June 10, 2023 • 4:00 PM - 11:00 PM</span>
                            </div>
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-map-marker-alt mr-3 text-primary"></i>
                                <span>Central Park, New York, NY</span>
                            </div>
                            <div class="flex items-center text-gray-600 dark:text-gray-400">
                                <i class="fas fa-users mr-3 text-primary"></i>
                                <span>1,247 people attending</span>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-semibold mb-3">About this event</h3>
                            <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                                Join us for an unforgettable evening of music featuring top artists from around the world. 
                                Experience live performances, food trucks, and a vibrant atmosphere in the heart of Central Park.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Form -->
            <div>
                <div class="bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-white/20 dark:border-gray-700/50">
                    <h2 class="text-2xl font-bold mb-6">Book Your Tickets</h2>
                    
                    <form class="space-y-6">
                        <!-- Ticket Selection -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Select Tickets</h3>
                            <div class="space-y-3">
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-primary transition">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">General Admission</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Access to main event area</p>
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <span class="text-lg font-bold text-primary">$49</span>
                                            <div class="flex items-center space-x-2">
                                                <button type="button" class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700">-</button>
                                                <span class="w-8 text-center">1</span>
                                                <button type="button" class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:border-primary transition">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium">VIP Access</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">Premium seating + backstage access</p>
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <span class="text-lg font-bold text-primary">$149</span>
                                            <div class="flex items-center space-x-2">
                                                <button type="button" class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700">-</button>
                                                <span class="w-8 text-center">0</span>
                                                <button type="button" class="w-8 h-8 rounded-full border border-gray-300 dark:border-gray-600 flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Personal Information -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Your Information</h3>
                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <label for="firstName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name</label>
                                    <input type="text" id="firstName" name="firstName" required 
                                           class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                </div>
                                <div>
                                    <label for="lastName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name</label>
                                    <input type="text" id="lastName" name="lastName" required 
                                           class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                </div>
                                <div class="sm:col-span-2">
                                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address</label>
                                    <input type="email" id="email" name="email" required 
                                           class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                </div>
                                <div class="sm:col-span-2">
                                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" 
                                           class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                </div>
                            </div>
                        </div>

                        <!-- Order Summary -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                            <h3 class="text-lg font-semibold mb-4">Order Summary</h3>
                            <div class="space-y-2 mb-4">
                                <div class="flex justify-between">
                                    <span>General Admission × 1</span>
                                    <span>$49.00</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Service Fee</span>
                                    <span>$4.90</span>
                                </div>
                                <div class="border-t border-gray-200 dark:border-gray-700 pt-2 flex justify-between font-bold text-lg">
                                    <span>Total</span>
                                    <span class="text-primary">$53.90</span>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <h3 class="text-lg font-semibold mb-4">Payment Method</h3>
                            <div class="space-y-3">
                                <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-primary transition cursor-pointer">
                                    <input type="radio" name="payment" value="card" class="mr-3" checked>
                                    <i class="fas fa-credit-card mr-3 text-primary"></i>
                                    <span>Credit/Debit Card</span>
                                </label>
                                <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-primary transition cursor-pointer">
                                    <input type="radio" name="payment" value="paypal" class="mr-3">
                                    <i class="fab fa-paypal mr-3 text-blue-500"></i>
                                    <span>PayPal</span>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" 
                                class="w-full py-4 bg-primary hover:bg-indigo-500 text-white font-medium rounded-lg transition duration-300 transform hover:scale-105">
                            <i class="fas fa-lock mr-2"></i>
                            Secure Checkout - $53.90
                        </button>

                        <p class="text-xs text-gray-500 text-center">
                            Your payment information is secure and encrypted. 
                            <a href="#" class="text-primary hover:underline">Learn more</a>
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js" defer></script>
</body>
</html>
