# Contributing to Eventify

Thank you for your interest in contributing to Eventify! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Bugs

1. **Check existing issues** to avoid duplicates
2. **Use the bug report template** when creating new issues
3. **Provide detailed information**:
   - Browser and version
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots if applicable

### Suggesting Features

1. **Check existing feature requests** to avoid duplicates
2. **Use the feature request template**
3. **Provide clear description** of the proposed feature
4. **Explain the use case** and benefits

### Code Contributions

#### Getting Started

1. **Fork the repository**
2. **Clone your fork**:
   ```bash
   git clone https://github.com/your-username/eventify.git
   cd eventify
   ```
3. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

#### Development Guidelines

##### Code Style
- Use **consistent indentation** (2 spaces)
- Follow **semantic HTML5** practices
- Use **BEM methodology** for CSS classes when applicable
- Write **clean, readable JavaScript** with proper comments
- Follow **ES6+ standards**

##### File Organization
- Keep **HTML structure** clean and semantic
- Organize **CSS** with logical sections and comments
- Write **modular JavaScript** with clear function purposes
- Use **meaningful variable and function names**

##### Testing
- Test on **multiple browsers** (Chrome, Firefox, Safari, Edge)
- Verify **responsive design** on different screen sizes
- Check **accessibility** with screen readers
- Validate **HTML and CSS** syntax

#### Making Changes

1. **Make your changes** following the guidelines above
2. **Test thoroughly** across different browsers and devices
3. **Commit with clear messages**:
   ```bash
   git commit -m "feat: add event filtering functionality"
   ```
4. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```
5. **Create a Pull Request**

### Pull Request Guidelines

#### Before Submitting
- [ ] Code follows the style guidelines
- [ ] Changes have been tested across browsers
- [ ] Responsive design works on mobile devices
- [ ] No console errors or warnings
- [ ] HTML validates without errors
- [ ] CSS is organized and commented
- [ ] JavaScript is clean and documented

#### PR Description
- **Clear title** describing the change
- **Detailed description** of what was changed and why
- **Screenshots** for UI changes
- **Testing notes** and browser compatibility
- **Related issues** (if applicable)

## 🎨 Design Guidelines

### Visual Consistency
- Follow the **established color scheme**:
  - Primary: `#6366f1` (Indigo)
  - Secondary: `#8b5cf6` (Purple)
  - Dark: `#0f172a` (Dark Blue)
- Use **Poppins font family** for consistency
- Maintain **consistent spacing** using Tailwind classes
- Follow **glassmorphism design** patterns

### Responsive Design
- **Mobile-first** approach
- Use **Tailwind breakpoints**: `sm:`, `md:`, `lg:`, `xl:`
- Test on **common device sizes**
- Ensure **touch-friendly** interactions

### Accessibility
- Use **semantic HTML** elements
- Provide **alt text** for images
- Ensure **keyboard navigation** works
- Maintain **color contrast** ratios
- Support **screen readers**

## 📝 Commit Message Format

Use the following format for commit messages:

```
type(scope): description

[optional body]

[optional footer]
```

### Types
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes (formatting, etc.)
- `refactor`: Code refactoring
- `test`: Adding or updating tests
- `chore`: Maintenance tasks

### Examples
```
feat(auth): add user login functionality
fix(modal): resolve scrolling issue on mobile
docs(readme): update installation instructions
style(css): improve button hover animations
```

## 🚀 Development Setup

### Prerequisites
- Modern web browser
- Text editor or IDE
- Basic knowledge of HTML, CSS, JavaScript

### Local Development
1. Open `Evnts.html` in your browser
2. Make changes to files
3. Refresh browser to see changes
4. Use browser dev tools for debugging

### Optional Tools
- **Live Server** extension for auto-refresh
- **Prettier** for code formatting
- **ESLint** for JavaScript linting
- **HTML Validator** for markup validation

## 📋 Issue Templates

### Bug Report
```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior.

**Expected behavior**
What you expected to happen.

**Screenshots**
If applicable, add screenshots.

**Browser Information:**
- Browser: [e.g. Chrome, Firefox]
- Version: [e.g. 91.0]
- OS: [e.g. Windows, macOS]
```

### Feature Request
```markdown
**Feature Description**
A clear description of the feature you'd like to see.

**Use Case**
Explain why this feature would be useful.

**Proposed Solution**
Describe how you envision this feature working.

**Additional Context**
Any other context or screenshots about the feature.
```

## 🎯 Areas for Contribution

### High Priority
- [ ] Backend API integration
- [ ] User authentication system
- [ ] Payment processing
- [ ] Database integration

### Medium Priority
- [ ] Advanced event filtering
- [ ] Calendar view
- [ ] Social media integration
- [ ] Email notifications

### Low Priority
- [ ] Multi-language support
- [ ] Advanced animations
- [ ] PWA features
- [ ] Performance optimizations

## 📞 Getting Help

- **GitHub Issues**: For bugs and feature requests
- **Discussions**: For questions and general discussion
- **Email**: [<EMAIL>] for direct contact

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- CHANGELOG.md for significant contributions
- GitHub contributors page

Thank you for helping make Eventify better! 🎉
