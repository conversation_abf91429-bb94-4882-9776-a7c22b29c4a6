// Eventify - Main JavaScript Functionality
// Author: Eventify Team
// Description: Handles all interactive features for the Eventify platform

class EventifyApp {
    constructor() {
        this.events = this.loadSampleEvents();
        this.currentFilter = 'all';
        this.currentSearchTerm = '';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.startCountdown();
        this.setupThemeToggle();
        this.setupMobileMenu();
        this.setupEventFilters();
        this.setupSearchFunctionality();
        this.setupEventCreation();
        this.renderEvents();
    }

    // Theme Toggle Functionality
    setupThemeToggle() {
        const themeToggle = document.getElementById('theme-toggle');
        const html = document.documentElement;
        
        // Check for saved theme preference or default to light mode
        const savedTheme = localStorage.getItem('theme') || 'light';
        html.classList.toggle('dark', savedTheme === 'dark');
        
        themeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
            const isDark = html.classList.contains('dark');
            localStorage.setItem('theme', isDark ? 'dark' : 'light');
            
            // Add animation effect
            themeToggle.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                themeToggle.style.transform = 'rotate(0deg)';
            }, 300);
        });
    }

    // Mobile Menu Toggle
    setupMobileMenu() {
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
            
            // Animate hamburger icon
            const icon = mobileMenuButton.querySelector('i');
            icon.classList.toggle('fa-bars');
            icon.classList.toggle('fa-times');
        });

        // Close mobile menu when clicking on links
        const mobileLinks = mobileMenu.querySelectorAll('a');
        mobileLinks.forEach(link => {
            link.addEventListener('click', () => {
                mobileMenu.classList.add('hidden');
                const icon = mobileMenuButton.querySelector('i');
                icon.classList.add('fa-bars');
                icon.classList.remove('fa-times');
            });
        });
    }

    // Countdown Timer for Hero Section
    startCountdown() {
        const targetDate = new Date();
        targetDate.setDate(targetDate.getDate() + 30); // 30 days from now
        
        const updateCountdown = () => {
            const now = new Date().getTime();
            const distance = targetDate.getTime() - now;
            
            if (distance < 0) {
                document.getElementById('countdown').innerHTML = '<div class="text-white text-xl">Event Started!</div>';
                return;
            }
            
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            document.getElementById('days').textContent = days.toString().padStart(2, '0');
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');
        };
        
        updateCountdown();
        setInterval(updateCountdown, 1000);
    }

    // Event Filtering System
    setupEventFilters() {
        const filterButtons = document.querySelectorAll('[data-filter]');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                // Remove active class from all buttons
                filterButtons.forEach(btn => {
                    btn.classList.remove('bg-primary', 'text-white');
                    btn.classList.add('bg-gray-100', 'dark:bg-gray-800');
                });
                
                // Add active class to clicked button
                e.target.classList.add('bg-primary', 'text-white');
                e.target.classList.remove('bg-gray-100', 'dark:bg-gray-800');
                
                // Update current filter
                this.currentFilter = e.target.dataset.filter || 'all';
                this.renderEvents();
            });
        });
    }

    // Search Functionality
    setupSearchFunctionality() {
        const searchInput = document.querySelector('input[placeholder="Search events..."]');
        const dateFilter = document.querySelector('select:nth-of-type(1)');
        const locationFilter = document.querySelector('select:nth-of-type(2)');
        const priceFilter = document.querySelector('select:nth-of-type(3)');
        
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.currentSearchTerm = e.target.value.toLowerCase();
                this.renderEvents();
            });
        }
        
        [dateFilter, locationFilter, priceFilter].forEach(filter => {
            if (filter) {
                filter.addEventListener('change', () => {
                    this.renderEvents();
                });
            }
        });
    }

    // Event Creation Form
    setupEventCreation() {
        const createEventForm = document.querySelector('.glass-card form') || 
                              document.querySelector('[data-event-form]');
        
        if (createEventForm) {
            createEventForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleEventCreation(new FormData(createEventForm));
            });
        }
    }

    handleEventCreation(formData) {
        const eventData = {
            id: Date.now(),
            name: formData.get('eventName') || 'New Event',
            type: formData.get('eventType') || 'general',
            startDate: formData.get('startDate'),
            endDate: formData.get('endDate'),
            location: formData.get('location') || 'TBD',
            price: Math.floor(Math.random() * 100) + 10, // Random price for demo
            image: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30',
            attendees: Math.floor(Math.random() * 50) + 5
        };
        
        this.events.unshift(eventData);
        this.renderEvents();
        this.showNotification('Event created successfully!', 'success');
    }

    // Load Sample Events Data
    loadSampleEvents() {
        return [
            {
                id: 1,
                name: 'Summer Music Festival',
                type: 'music',
                date: 'Sat, Jun 10 • 4:00 PM',
                location: 'Central Park, New York',
                price: 49,
                image: 'https://images.unsplash.com/photo-1470229722913-7c0e2dbbafd3',
                attendees: 15,
                status: 'trending'
            },
            {
                id: 2,
                name: 'Community Yoga in the Park',
                type: 'sports',
                date: 'Sun, Jun 11 • 9:00 AM',
                location: 'Riverside Park, New York',
                price: 0,
                image: 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30',
                attendees: 10,
                status: 'free'
            },
            {
                id: 3,
                name: 'Tech Conference 2023',
                type: 'technology',
                date: 'Fri-Sun, Jun 16-18 • 9:00 AM',
                location: 'Javits Center, New York',
                price: 199,
                image: 'https://images.unsplash.com/photo-1531058020387-3be344556be6',
                attendees: 28,
                status: 'last-tickets'
            }
        ];
    }

    // Render Events to DOM
    renderEvents() {
        const eventsContainer = document.querySelector('.grid.grid-cols-1.sm\\:grid-cols-2.lg\\:grid-cols-3.gap-8');
        if (!eventsContainer) return;
        
        const filteredEvents = this.filterEvents();
        eventsContainer.innerHTML = filteredEvents.map(event => this.createEventCard(event)).join('');
        
        // Add event listeners to new cards
        this.setupEventCardListeners();
    }

    filterEvents() {
        return this.events.filter(event => {
            const matchesFilter = this.currentFilter === 'all' || event.type === this.currentFilter;
            const matchesSearch = event.name.toLowerCase().includes(this.currentSearchTerm) ||
                                event.location.toLowerCase().includes(this.currentSearchTerm);
            return matchesFilter && matchesSearch;
        });
    }

    createEventCard(event) {
        const statusBadge = this.getStatusBadge(event);
        const priceDisplay = event.price === 0 ? 'Free' : `$${event.price}`;
        
        return `
            <div class="event-card bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition duration-300" data-event-id="${event.id}">
                <div class="relative overflow-hidden h-48">
                    <div class="event-image absolute inset-0 bg-[url('${event.image}')] bg-cover bg-center transition duration-500"></div>
                    ${statusBadge}
                </div>
                <div class="p-6">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="text-xl font-bold">${event.name}</h3>
                        <span class="text-lg font-bold text-primary">${priceDisplay}</span>
                    </div>
                    <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                        <i class="fas fa-calendar-alt mr-2"></i>
                        <span>${event.date}</span>
                    </div>
                    <div class="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        <span>${event.location}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <div class="flex -space-x-2">
                            ${this.generateAttendeeAvatars(event.attendees)}
                        </div>
                        <button class="event-details-btn px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg text-sm font-medium transition" data-event-id="${event.id}">Details</button>
                    </div>
                </div>
            </div>
        `;
    }

    getStatusBadge(event) {
        if (!event.status) return '';
        
        const badges = {
            'trending': '<span class="px-3 py-1 bg-primary text-white text-xs font-semibold rounded-full">Trending</span>',
            'free': '<span class="px-3 py-1 bg-green-500 text-white text-xs font-semibold rounded-full">Free</span>',
            'last-tickets': '<span class="px-3 py-1 bg-yellow-500 text-white text-xs font-semibold rounded-full">Last Tickets</span>',
            'sold-out': '<span class="px-3 py-1 bg-red-500 text-white text-xs font-semibold rounded-full">Sold Out</span>'
        };
        
        return `<div class="absolute top-4 right-4">${badges[event.status] || ''}</div>`;
    }

    generateAttendeeAvatars(count) {
        const avatars = [];
        const maxVisible = 3;
        const visibleCount = Math.min(count, maxVisible);
        
        for (let i = 0; i < visibleCount; i++) {
            const randomId = Math.floor(Math.random() * 100);
            const gender = Math.random() > 0.5 ? 'women' : 'men';
            avatars.push(`<img class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800" src="https://randomuser.me/api/portraits/${gender}/${randomId}.jpg" alt="Attendee">`);
        }
        
        if (count > maxVisible) {
            avatars.push(`<div class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 bg-gray-100 dark:bg-gray-700 flex items-center justify-center text-xs">+${count - maxVisible}</div>`);
        }
        
        return avatars.join('');
    }

    setupEventCardListeners() {
        const detailButtons = document.querySelectorAll('.event-details-btn');
        detailButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const eventId = parseInt(e.target.dataset.eventId);
                this.showEventDetails(eventId);
            });
        });
    }

    showEventDetails(eventId) {
        const event = this.events.find(e => e.id === eventId);
        if (!event) return;
        
        this.showModal(`
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-auto">
                <h2 class="text-2xl font-bold mb-4">${event.name}</h2>
                <img src="${event.image}" alt="${event.name}" class="w-full h-48 object-cover rounded-lg mb-4">
                <div class="space-y-2 mb-4">
                    <p><i class="fas fa-calendar-alt mr-2 text-primary"></i>${event.date}</p>
                    <p><i class="fas fa-map-marker-alt mr-2 text-primary"></i>${event.location}</p>
                    <p><i class="fas fa-ticket-alt mr-2 text-primary"></i>${event.price === 0 ? 'Free' : '$' + event.price}</p>
                    <p><i class="fas fa-users mr-2 text-primary"></i>${event.attendees} attending</p>
                </div>
                <div class="flex space-x-2">
                    <button class="flex-1 px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg transition" onclick="eventifyApp.bookEvent(${event.id})">Book Now</button>
                    <button class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition" onclick="eventifyApp.closeModal()">Close</button>
                </div>
            </div>
        `);
    }

    bookEvent(eventId) {
        this.showNotification('Event booked successfully! Check your email for details.', 'success');
        this.closeModal();
    }

    showModal(content) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4';
        modal.innerHTML = content;
        document.body.appendChild(modal);
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) this.closeModal();
        });
    }

    closeModal() {
        const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
        if (modal) modal.remove();
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        
        notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    setupEventListeners() {
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Handle all button navigation
        this.setupButtonNavigation();
    }

    setupButtonNavigation() {
        // Navigation mapping for all buttons and links
        const navigationMap = {
            // Authentication
            'Log In': 'login.html',
            'Sign Up': 'signup.html',
            'Sign Up Free': 'signup.html',

            // Event actions
            'View All Events': 'events.html',
            'Explore Events': 'events.html',
            'Start Creating': 'create-event.html',
            'Create Event': 'create-event.html',
            'Continue Setup': 'create-event.html',
            'Get Tickets': 'booking.html',

            // Information pages
            'Learn More': 'about.html',
            'About Us': 'about.html',
            'Contact': 'contact.html',
            'Blog': 'blog.html',
            'Careers': 'careers.html',
            'Pricing': 'pricing.html',
            'Resources': 'resources.html',
            'Community': 'community.html',

            // Legal pages
            'Privacy Policy': 'privacy.html',
            'Terms of Service': 'terms.html',
            'Cookies': 'cookies.html',

            // Event categories
            'Popular Events': 'events.html?filter=popular',
            'Upcoming Events': 'events.html?filter=upcoming',
            'Nearby Events': 'events.html?filter=nearby',
            'Online Events': 'events.html?filter=online'
        };

        // Add click handlers to all buttons and links
        document.addEventListener('click', (e) => {
            const element = e.target.closest('a, button');
            if (!element) return;

            const text = element.textContent.trim();
            const href = element.getAttribute('href');

            // Handle navigation based on text content
            if (navigationMap[text]) {
                e.preventDefault();
                this.navigateToPage(navigationMap[text]);
                return;
            }

            // Handle specific href patterns
            if (href === '#') {
                e.preventDefault();

                // Handle special cases based on context
                if (element.classList.contains('event-details-btn') || text === 'Details') {
                    const eventId = element.dataset.eventId || 1;
                    this.showEventDetails(parseInt(eventId));
                } else if (text.includes('Tickets') || text.includes('Book')) {
                    this.navigateToPage('booking.html');
                } else if (element.closest('.social-links') || element.querySelector('i[class*="fab"]')) {
                    // Social media links - show coming soon message
                    this.showNotification('Social media integration coming soon!', 'info');
                } else {
                    // Generic placeholder for other # links
                    this.showNotification(`${text} feature coming soon!`, 'info');
                }
            }
        });

        // Handle form submissions
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.tagName === 'FORM') {
                e.preventDefault();
                this.handleFormSubmission(form);
            }
        });
    }

    navigateToPage(url) {
        // Check if page exists, otherwise show placeholder
        const existingPages = [
            'login.html', 'signup.html', 'create-event.html',
            'events.html', 'Evnts.html'
        ];

        if (existingPages.includes(url)) {
            window.location.href = url;
        } else {
            // Show placeholder page or coming soon message
            this.showComingSoonModal(url);
        }
    }

    showComingSoonModal(pageName) {
        const pageTitle = pageName.replace('.html', '').replace('-', ' ')
            .split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

        this.showModal(`
            <div class="bg-white dark:bg-gray-800 rounded-lg p-8 max-w-md mx-auto text-center">
                <div class="mb-6">
                    <i class="fas fa-rocket text-6xl text-primary mb-4"></i>
                    <h2 class="text-2xl font-bold mb-2">Coming Soon!</h2>
                    <p class="text-gray-600 dark:text-gray-400">
                        The <strong>${pageTitle}</strong> page is under development and will be available soon.
                    </p>
                </div>
                <div class="space-y-3">
                    <button onclick="eventifyApp.closeModal()"
                            class="w-full px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg transition">
                        Got it!
                    </button>
                    <button onclick="eventifyApp.closeModal(); window.location.href='Evnts.html'"
                            class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition">
                        Back to Home
                    </button>
                </div>
            </div>
        `);
    }

    handleFormSubmission(form) {
        const formData = new FormData(form);
        const formType = this.detectFormType(form);

        switch (formType) {
            case 'login':
                this.handleLogin(formData);
                break;
            case 'signup':
                this.handleSignup(formData);
                break;
            case 'create-event':
                this.handleEventCreation(formData);
                break;
            case 'contact':
                this.handleContact(formData);
                break;
            default:
                this.showNotification('Form submitted successfully!', 'success');
        }
    }

    detectFormType(form) {
        const action = form.getAttribute('action') || '';
        const inputs = form.querySelectorAll('input[name]');
        const inputNames = Array.from(inputs).map(input => input.name);

        if (inputNames.includes('email') && inputNames.includes('password') && !inputNames.includes('firstName')) {
            return 'login';
        } else if (inputNames.includes('firstName') || inputNames.includes('confirmPassword')) {
            return 'signup';
        } else if (inputNames.includes('eventName')) {
            return 'create-event';
        } else if (inputNames.includes('message')) {
            return 'contact';
        }
        return 'generic';
    }

    handleLogin(formData) {
        const email = formData.get('email');
        const password = formData.get('password');

        // Simulate login process
        this.showNotification('Logging in...', 'info');

        setTimeout(() => {
            // Simulate successful login
            localStorage.setItem('user', JSON.stringify({
                email: email,
                name: email.split('@')[0],
                loggedIn: true
            }));

            this.showNotification('Login successful! Welcome back!', 'success');

            setTimeout(() => {
                window.location.href = 'Evnts.html';
            }, 1500);
        }, 1000);
    }

    handleSignup(formData) {
        const firstName = formData.get('firstName');
        const lastName = formData.get('lastName');
        const email = formData.get('email');

        // Simulate signup process
        this.showNotification('Creating your account...', 'info');

        setTimeout(() => {
            // Simulate successful signup
            localStorage.setItem('user', JSON.stringify({
                email: email,
                name: `${firstName} ${lastName}`,
                loggedIn: true
            }));

            this.showNotification('Account created successfully! Welcome to Eventify!', 'success');

            setTimeout(() => {
                window.location.href = 'Evnts.html';
            }, 1500);
        }, 1000);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.eventifyApp = new EventifyApp();
});
