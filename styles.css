/* Eventify - Main Stylesheet */
/* Author: <PERSON><PERSON><PERSON><PERSON> */
/* Description: All custom styles for the Eventify platform */
/* Version: 1.0.0 */
/* Last Updated: 2023 */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Base Styles */
body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8fafc;
}

.dark body {
    background-color: #0f172a;
}

/* Hero Section Gradient */
.hero-gradient {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%);
}

/* Glass Card Effect */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-card {
    background: rgba(15, 23, 42, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Event Card Hover Effects */
.event-card:hover .event-image {
    transform: scale(1.05);
}

/* Ticket Option Hover Effects */
.ticket-option:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.dark .ticket-option:hover {
    box-shadow: 0 10px 25px -5px rgba(255, 255, 255, 0.1);
}

/* Countdown Timer Styles */
.countdown-number {
    perspective: 1000px;
}

.countdown-flip {
    transform-style: preserve-3d;
    transition: transform 0.6s;
}

.flip {
    transform: rotateX(180deg);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
    background: #1e293b;
}

::-webkit-scrollbar-thumb {
    background: #6366f1;
    border-radius: 10px;
}

.dark ::-webkit-scrollbar-thumb {
    background: #8b5cf6;
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out forwards;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-out forwards;
}

/* Animation Delays */
.delay-100 {
    animation-delay: 0.1s;
}

.delay-200 {
    animation-delay: 0.2s;
}

.delay-300 {
    animation-delay: 0.3s;
}

.delay-400 {
    animation-delay: 0.4s;
}

.delay-500 {
    animation-delay: 0.5s;
}

/* Keyframe Animations */
@keyframes fadeIn {
    0% { 
        opacity: 0; 
    }
    100% { 
        opacity: 1; 
    }
}

@keyframes slideUp {
    0% { 
        transform: translateY(20px); 
        opacity: 0; 
    }
    100% { 
        transform: translateY(0); 
        opacity: 1; 
    }
}

/* Additional Custom Styles */

/* Navigation Enhancements */
nav {
    transition: all 0.3s ease;
}

/* Button Hover Effects */
button {
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-1px);
}

/* Form Input Styles */
input:focus,
select:focus,
textarea:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.15);
}

/* Card Hover Effects */
.bg-white:hover,
.dark .bg-gray-800:hover {
    transform: translateY(-2px);
}

/* Link Hover Effects */
a {
    transition: all 0.3s ease;
}

/* Icon Animations */
i {
    transition: all 0.3s ease;
}

/* Theme Toggle Animation */
#theme-toggle {
    transition: transform 0.3s ease;
}

/* Mobile Menu Animation */
#mobile-menu {
    transition: all 0.3s ease;
}

/* Event Image Transition */
.event-image {
    transition: transform 0.5s ease;
}

/* Notification Styles */
.notification {
    transition: transform 0.3s ease;
}

/* Modal Styles */
.modal-backdrop {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .hero-gradient {
        background: linear-gradient(180deg, rgba(99, 102, 241, 0.9) 0%, rgba(139, 92, 246, 0.9) 100%);
    }
    
    .glass-card {
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .glass-card {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid #000;
    }
    
    .dark .glass-card {
        background: rgba(0, 0, 0, 0.95);
        border: 2px solid #fff;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
}

/* Skip Link for Accessibility */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #6366f1;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
}

.skip-link:focus {
    top: 6px;
}

/* Creative Background Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes float-delayed {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-30px) rotate(180deg);
    }
}

@keyframes bounce-slow {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes pulse-slow {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes spin-slow {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Animation Classes */
.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
}

.animate-bounce-slow {
    animation: bounce-slow 4s ease-in-out infinite;
}

.animate-pulse-slow {
    animation: pulse-slow 4s ease-in-out infinite;
}

.animate-spin-slow {
    animation: spin-slow 20s linear infinite;
}

/* Enhanced Glassmorphism Effects */
.backdrop-blur-lg {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
}

.backdrop-blur-xl {
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
}

/* Creative Background Patterns */
.pattern-dots {
    background-image: radial-gradient(circle, #6366f1 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.1;
}

.pattern-grid {
    background-image:
        linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
}

/* Enhanced Card Effects */
.glass-card-enhanced {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-card-enhanced {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Enhanced Button Visibility */
.back-to-home-btn {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.dark .back-to-home-btn {
    background: rgba(15, 23, 42, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.back-to-home-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.dark .back-to-home-btn:hover {
    background: rgba(15, 23, 42, 0.5);
}
