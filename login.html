<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login | Eventify</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                        dark: '#0f172a',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-dark text-gray-800 dark:text-gray-200 min-h-screen relative">
    <!-- Animated Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-primary/20 via-secondary/10 to-pink-500/20 z-0"></div>
    <div class="fixed inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%236366f1" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40 z-0"></div>

    <!-- Floating Elements -->
    <div class="fixed top-20 left-20 w-32 h-32 bg-primary/10 rounded-full blur-xl animate-pulse z-0"></div>
    <div class="fixed bottom-32 right-20 w-40 h-40 bg-secondary/10 rounded-full blur-xl animate-pulse delay-1000 z-0"></div>
    <div class="fixed top-1/2 left-10 w-24 h-24 bg-pink-500/10 rounded-full blur-xl animate-pulse delay-500 z-0"></div>
    <div class="min-h-screen flex items-center justify-center py-12 px-4">
        <div class="max-w-md w-full relative z-10">
        <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20 dark:border-gray-700/50">
            <!-- Header -->
            <div class="text-center mb-8">
                <a href="Evnts.html" class="inline-block">
                    <span class="text-primary text-3xl font-bold">Eventify</span>
                </a>
                <h2 class="text-2xl font-bold mt-4 mb-2">Welcome Back!</h2>
                <p class="text-gray-600 dark:text-gray-400">Sign in to your account</p>
            </div>

            <!-- Login Form -->
            <form class="space-y-6">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email Address</label>
                    <input type="email" id="email" name="email" required 
                           class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition"
                           placeholder="Enter your email">
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Password</label>
                    <div class="relative">
                        <input type="password" id="password" name="password" required 
                               class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition"
                               placeholder="Enter your password">
                        <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input type="checkbox" id="remember" name="remember" 
                               class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="remember" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">Remember me</label>
                    </div>
                    <a href="forgot-password.html" class="text-sm text-primary hover:underline">Forgot password?</a>
                </div>

                <button type="submit" 
                        class="w-full py-3 px-4 bg-primary hover:bg-indigo-500 text-white font-medium rounded-lg transition duration-300 transform hover:scale-105">
                    Sign In
                </button>
            </form>

            <!-- Social Login -->
            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white dark:bg-gray-800 text-gray-500">Or continue with</span>
                    </div>
                </div>

                <div class="mt-6 grid grid-cols-2 gap-3">
                    <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition">
                        <i class="fab fa-google text-red-500 mr-2"></i>
                        Google
                    </button>
                    <button class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition">
                        <i class="fab fa-facebook text-blue-500 mr-2"></i>
                        Facebook
                    </button>
                </div>
            </div>

            <!-- Sign Up Link -->
            <div class="mt-6 text-center">
                <p class="text-sm text-gray-600 dark:text-gray-400">
                    Don't have an account? 
                    <a href="signup.html" class="text-primary hover:underline font-medium">Sign up here</a>
                </p>
            </div>
        </div>

        <!-- Back to Home -->
        <div class="text-center mt-6 relative z-20">
            <a href="Evnts.html" class="inline-flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-primary back-to-home-btn rounded-lg transition font-medium">
                <i class="fas fa-arrow-left mr-2"></i>Back to Home
            </a>
        </div>
        </div>
    </div>

    <script src="script.js" defer></script>
</body>
</html>
