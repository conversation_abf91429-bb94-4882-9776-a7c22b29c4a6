# 🎉 Eventify - Event Discovery & Hosting Platform

A modern, responsive web application for discovering and hosting amazing events. Built with vanilla HTML, CSS, JavaScript, and styled with Tailwind CSS.

![Eventify Preview](https://images.unsplash.com/photo-1511578314322-379afb476865?ixlib=rb-4.0.3&w=1200&h=600&fit=crop)

## ✨ Features

### 🔍 **Event Discovery**
- Browse events by category (Music, Sports, Business, Food & Drink, Arts, Technology)
- Real-time search functionality
- Advanced filtering by date, location, and price
- Interactive event cards with attendee information
- Event details modal with booking functionality

### 🎫 **Event Hosting**
- Create new events with intuitive form interface
- Event management tools
- Real-time analytics and reporting
- Marketing tools integration

### 🎨 **User Experience**
- **Dark/Light Mode**: Toggle between themes with persistence
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Smooth Animations**: CSS transitions and JavaScript animations
- **Live Countdown**: Real-time countdown timer for featured events
- **Mobile Menu**: Collapsible navigation for mobile devices
- **Toast Notifications**: Success/error feedback system

### 🚀 **Modern Features**
- Glassmorphism design effects
- Custom scrollbar styling
- Hover animations and transitions
- Smooth scrolling navigation
- Dynamic content rendering
- Local storage for user preferences

## 🛠️ Tech Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: Tailwind CSS (via CDN)
- **Icons**: Font Awesome 6.4.0
- **Fonts**: Google Fonts (Poppins)
- **Architecture**: Object-Oriented JavaScript with ES6 Classes

## 📁 Project Structure

```
eventify/
├── Evnts.html          # Main homepage
├── login.html          # User login page
├── signup.html         # User registration page
├── create-event.html   # Event creation form
├── events.html         # All events listing page
├── booking.html        # Event booking/ticket purchase
├── styles.css          # Custom CSS styles
├── script.js           # JavaScript functionality
├── README.md           # Project documentation
└── assets/             # (Future: images, additional assets)
```

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- No additional software required!

### Installation & Setup

1. **Clone or Download** the project files
2. **Open** `Evnts.html` in your web browser
3. **That's it!** The application runs entirely in the browser

### Alternative: Local Server (Optional)

For development or testing purposes, you can run a local server:

```bash
# Using Python
python -m http.server 8000

# Using Node.js
npx http-server -p 8000

# Using PHP
php -S localhost:8000
```

Then visit `http://localhost:8000/Evnts.html`

## 🎮 How to Use

### For Event Attendees

1. **Browse Events**: Scroll through the event grid or use category filters
2. **Search**: Use the search bar to find specific events
3. **Filter**: Apply date, location, and price filters
4. **View Details**: Click "Details" on any event card
5. **Book Event**: Click "Book Now" in the event details modal
6. **Toggle Theme**: Click the moon/sun icon to switch between dark/light mode

### For Event Hosts

1. **Navigate to Host Section**: Click "Host" in the navigation or scroll to the host section
2. **Fill Event Form**: Enter event details (name, type, dates, location)
3. **Create Event**: Click "Continue Setup" to create your event
4. **Manage Events**: View and manage your created events

## 🔧 Customization

### Adding New Event Categories

Edit the filter buttons in `Evnts.html`:

```html
<button class="px-6 py-2 bg-gray-100 dark:bg-gray-800 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition" data-filter="your-category">Your Category</button>
```

### Modifying Sample Events

Edit the `loadSampleEvents()` method in `script.js`:

```javascript
loadSampleEvents() {
    return [
        {
            id: 1,
            name: 'Your Event Name',
            type: 'category',
            date: 'Date String',
            location: 'Event Location',
            price: 50,
            image: 'image-url',
            attendees: 25,
            status: 'trending' // optional
        }
        // Add more events...
    ];
}
```

### Styling Customization

The project uses Tailwind CSS. You can:
- Modify existing classes in the HTML
- Add custom CSS in the `<style>` section
- Customize Tailwind configuration in the `<script>` section

## 🎨 Color Scheme

```css
:root {
    --primary: #6366f1;     /* Indigo */
    --secondary: #8b5cf6;   /* Purple */
    --dark: #0f172a;        /* Dark Blue */
}
```

## 📱 Browser Support

- ✅ Chrome 60+
- ✅ Firefox 60+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## 🔮 Future Enhancements

### Planned Features
- [ ] User authentication system
- [ ] Payment integration for ticket sales
- [ ] Event calendar view
- [ ] Social media sharing
- [ ] Email notifications
- [ ] Event reviews and ratings
- [ ] Advanced analytics dashboard
- [ ] Multi-language support

### Technical Improvements
- [ ] Convert to modern framework (React/Angular/Vue)
- [ ] Add backend API integration
- [ ] Implement state management
- [ ] Add unit and integration tests
- [ ] Progressive Web App (PWA) features
- [ ] Performance optimizations

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👨‍💻 Author

Created with ❤️ by the Eventify Team

## 🙏 Acknowledgments

- **Tailwind CSS** for the utility-first CSS framework
- **Font Awesome** for the beautiful icons
- **Google Fonts** for the Poppins font family
- **Unsplash** for the high-quality event images
- **Random User API** for the attendee avatars

## 📞 Support

If you have any questions or need help, please:
- Open an issue on GitHub
- Check the documentation
- Contact the development team

---

**Happy Event Planning! 🎊**
