<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Event | Eventify</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                        dark: '#0f172a',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-dark text-gray-800 dark:text-gray-200 relative">
    <!-- Creative Background for Event Creation -->
    <div class="fixed inset-0 bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900/20"></div>
    <div class="fixed inset-0 bg-[url('data:image/svg+xml,%3Csvg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%236366f1" fill-opacity="0.03"%3E%3Cpath d="M0 0h40v40H0V0zm40 40h40v40H40V40zm0-40h2l-2 2V0zm0 4l4-4h2l-6 6V4zm0 4l8-8h2L40 10V8zm0 4L52 0h2L40 14v-2zm0 4L56 0h2L40 18v-2zm0 4L60 0h2L40 22v-2zm0 4L64 0h2L40 26v-2zm0 4L68 0h2L40 30v-2zm0 4L72 0h2L40 34v-2zm0 4L76 0h2L40 38v-2zm0 4L80 0v2L42 40h-2zm4 0L80 4v2L46 40h-2zm4 0L80 8v2L50 40h-2zm4 0l28-28v2L54 40h-2zm4 0l24-24v2L58 40h-2zm4 0l20-20v2L62 40h-2zm4 0l16-16v2L66 40h-2zm4 0l12-12v2L70 40h-2zm4 0l8-8v2l-6 6h-2zm4 0l4-4v2L78 40h-2z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

    <!-- Floating Creative Elements -->
    <div class="fixed top-20 left-20 w-32 h-32 bg-gradient-to-br from-primary/10 to-secondary/10 rounded-full blur-2xl animate-float"></div>
    <div class="fixed bottom-20 right-20 w-40 h-40 bg-gradient-to-br from-pink-500/10 to-purple-500/10 rounded-full blur-2xl animate-float-delayed"></div>
    <div class="fixed top-1/2 right-10 w-24 h-24 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-full blur-xl animate-pulse"></div>
    <!-- Navigation -->
    <nav class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg shadow-sm border-b border-gray-200 dark:border-gray-700 relative z-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="Evnts.html" class="flex items-center">
                        <span class="text-primary text-2xl font-bold">Eventify</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="Evnts.html" class="text-gray-600 dark:text-gray-400 hover:text-primary transition">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Home
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12 relative z-10">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-4">Create Your Event</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">Share your passion with the world</p>
        </div>

        <!-- Progress Steps -->
        <div class="mb-12">
            <div class="flex items-center justify-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <span class="ml-2 text-sm font-medium text-primary">Basic Info</span>
                </div>
                <div class="w-16 h-1 bg-gray-200 dark:bg-gray-700"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Details</span>
                </div>
                <div class="w-16 h-1 bg-gray-200 dark:bg-gray-700"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-200 dark:bg-gray-700 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <span class="ml-2 text-sm font-medium text-gray-500">Publish</span>
                </div>
            </div>
        </div>

        <!-- Event Creation Form -->
        <div class="bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-2xl shadow-2xl p-8 border border-white/20 dark:border-gray-700/50">
            <form class="space-y-8">
                <!-- Basic Information -->
                <div>
                    <h2 class="text-2xl font-bold mb-6">Basic Information</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="md:col-span-2">
                            <label for="eventName" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event Name *</label>
                            <input type="text" id="eventName" name="eventName" required 
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition"
                                   placeholder="Enter your event name">
                        </div>

                        <div>
                            <label for="eventType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event Type *</label>
                            <select id="eventType" name="eventType" required 
                                    class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                <option value="">Select event type</option>
                                <option value="music">Music</option>
                                <option value="sports">Sports</option>
                                <option value="business">Business</option>
                                <option value="food">Food & Drink</option>
                                <option value="arts">Arts</option>
                                <option value="technology">Technology</option>
                                <option value="education">Education</option>
                                <option value="health">Health & Wellness</option>
                            </select>
                        </div>

                        <div>
                            <label for="eventFormat" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event Format *</label>
                            <select id="eventFormat" name="eventFormat" required 
                                    class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                <option value="">Select format</option>
                                <option value="in-person">In-Person</option>
                                <option value="virtual">Virtual</option>
                                <option value="hybrid">Hybrid</option>
                            </select>
                        </div>

                        <div>
                            <label for="startDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Date *</label>
                            <input type="datetime-local" id="startDate" name="startDate" required 
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition">
                        </div>

                        <div>
                            <label for="endDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date *</label>
                            <input type="datetime-local" id="endDate" name="endDate" required 
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition">
                        </div>

                        <div class="md:col-span-2">
                            <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location *</label>
                            <input type="text" id="location" name="location" required 
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition"
                                   placeholder="Enter venue name or address">
                        </div>

                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Event Description *</label>
                            <textarea id="description" name="description" rows="4" required 
                                      class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition"
                                      placeholder="Describe your event..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Ticketing -->
                <div>
                    <h2 class="text-2xl font-bold mb-6">Ticketing</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="ticketType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ticket Type *</label>
                            <select id="ticketType" name="ticketType" required 
                                    class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition">
                                <option value="free">Free</option>
                                <option value="paid">Paid</option>
                            </select>
                        </div>

                        <div>
                            <label for="ticketPrice" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Ticket Price ($)</label>
                            <input type="number" id="ticketPrice" name="ticketPrice" min="0" step="0.01" 
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition"
                                   placeholder="0.00">
                        </div>

                        <div>
                            <label for="maxAttendees" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Max Attendees</label>
                            <input type="number" id="maxAttendees" name="maxAttendees" min="1" 
                                   class="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition"
                                   placeholder="Unlimited">
                        </div>
                    </div>
                </div>

                <!-- Event Image -->
                <div>
                    <h2 class="text-2xl font-bold mb-6">Event Image</h2>
                    
                    <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <p class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">Upload Event Image</p>
                        <p class="text-sm text-gray-500 mb-4">PNG, JPG up to 10MB</p>
                        <input type="file" id="eventImage" name="eventImage" accept="image/*" class="hidden">
                        <button type="button" onclick="document.getElementById('eventImage').click()" 
                                class="px-6 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg transition">
                            Choose File
                        </button>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0 sm:space-x-4 pt-8 border-t border-gray-200 dark:border-gray-700">
                    <a href="Evnts.html" class="text-gray-600 dark:text-gray-400 hover:text-primary transition">
                        <i class="fas fa-arrow-left mr-2"></i>Cancel
                    </a>
                    
                    <div class="flex space-x-4">
                        <button type="button" class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition">
                            Save Draft
                        </button>
                        <button type="submit" class="px-8 py-3 bg-primary hover:bg-indigo-500 text-white rounded-lg font-medium transition">
                            Continue
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script src="script.js" defer></script>
</body>
</html>
