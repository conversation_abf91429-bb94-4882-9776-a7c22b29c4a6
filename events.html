<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Events | Eventify</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                        dark: '#0f172a',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 dark:bg-dark text-gray-800 dark:text-gray-200 relative">
    <!-- Dynamic Events Background -->
    <div class="fixed inset-0 bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-indigo-900/20 dark:to-purple-900/20"></div>
    <div class="fixed inset-0 bg-[url('data:image/svg+xml,%3Csvg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M9 0h2v20H9V0zm25.134.84l1.732 1-10 17.32-1.732-1 10-17.32zm-2.732 2.732l1.732 1-10 17.32-1.732-1 10-17.32zm2.732 2.732l1.732 1-10 17.32-1.732-1 10-17.32zm-2.732 2.732l1.732 1-10 17.32-1.732-1 10-17.32zm2.732 2.732l1.732 1-10 17.32-1.732-1 10-17.32zm-2.732 2.732l1.732 1-10 17.32-1.732-1 10-17.32zm2.732 2.732l1.732 1-10 17.32-1.732-1 10-17.32z" fill="%236366f1" fill-opacity="0.05"/%3E%3C/svg%3E')] opacity-30"></div>

    <!-- Event-themed floating elements -->
    <div class="fixed top-16 right-16 w-28 h-28 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full blur-xl animate-bounce-slow"></div>
    <div class="fixed bottom-16 left-16 w-36 h-36 bg-gradient-to-br from-green-400/20 to-blue-500/20 rounded-full blur-xl animate-pulse-slow"></div>
    <div class="fixed top-1/3 left-1/4 w-20 h-20 bg-gradient-to-br from-pink-400/20 to-purple-500/20 rounded-full blur-lg animate-float"></div>
    <!-- Navigation -->
    <nav class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-lg shadow-sm border-b border-gray-200 dark:border-gray-700 relative z-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="Evnts.html" class="flex items-center">
                        <span class="text-primary text-2xl font-bold">Eventify</span>
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition">
                        <i class="fas fa-moon dark:hidden"></i>
                        <i class="fas fa-sun hidden dark:block"></i>
                    </button>
                    <a href="login.html" class="px-4 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition">Log In</a>
                    <a href="signup.html" class="px-4 py-2 bg-primary hover:bg-indigo-500 text-white rounded-lg transition">Sign Up</a>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 relative z-10">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold mb-4">All Events</h1>
            <p class="text-lg text-gray-600 dark:text-gray-400">Discover amazing events happening around you</p>
        </div>

        <!-- Filters -->
        <div class="mb-8 bg-white/90 dark:bg-gray-800/90 backdrop-blur-lg rounded-xl p-6 shadow-lg border border-white/20 dark:border-gray-700/50">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <input type="text" placeholder="Search events..." 
                           class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                </div>
                <div>
                    <select class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>All Categories</option>
                        <option>Music</option>
                        <option>Sports</option>
                        <option>Business</option>
                        <option>Food & Drink</option>
                        <option>Arts</option>
                        <option>Technology</option>
                    </select>
                </div>
                <div>
                    <select class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>Any Date</option>
                        <option>Today</option>
                        <option>Tomorrow</option>
                        <option>This Week</option>
                        <option>This Weekend</option>
                        <option>Next Week</option>
                    </select>
                </div>
                <div>
                    <select class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option>Any Price</option>
                        <option>Free</option>
                        <option>Under $25</option>
                        <option>$25 - $50</option>
                        <option>$50 - $100</option>
                        <option>Over $100</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Events Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="events-container">
            <!-- Events will be loaded here by JavaScript -->
        </div>

        <!-- Load More -->
        <div class="text-center mt-12">
            <button class="px-8 py-3 bg-primary hover:bg-indigo-500 text-white rounded-lg font-medium transition">
                Load More Events
            </button>
        </div>
    </div>

    <script src="script.js" defer></script>
</body>
</html>
